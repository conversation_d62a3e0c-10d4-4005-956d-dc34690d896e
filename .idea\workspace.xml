<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="031b2ea2-4304-4e1f-808f-ba85151d3cd4" name="更改" comment="feat: 添加更多管道中轴线数据">
      <change afterPath="$PROJECT_DIR$/public/texture/deng_002.jpg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/aobo/fieldView/threeJs/service/core.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/aobo/fieldView/threeJs/service/core.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/aobo/fieldView/threeJs/service/environment.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/aobo/fieldView/threeJs/service/environment.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30zJLl0puAkEZFoR5CtEhU3Lr9K" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/Vankey/power-grid/csg-dts-web/public/model&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;to.speed.mode.migration.done&quot;: &quot;true&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\JetBrains\\WebStorm 2024.1.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\Vankey\power-grid\csg-dts-web\public\model" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\assets\center-line" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\public" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\store\modules" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\views\aobo\fieldView" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\Vankey\power-grid\csg-dts-web\public\texture" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\assets\center-line" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\assets" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="031b2ea2-4304-4e1f-808f-ba85151d3cd4" name="更改" comment="" />
      <created>1754624415863</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754624415863</updated>
      <workItem from="1754624418213" duration="5939000" />
      <workItem from="1754874806925" duration="1369000" />
      <workItem from="1754876368486" duration="2173000" />
      <workItem from="1754878594105" duration="21158000" />
      <workItem from="1754961115606" duration="16777000" />
      <workItem from="1755056249347" duration="10993000" />
      <workItem from="1755136584108" duration="8635000" />
      <workItem from="1755220160900" duration="15132000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 项目初始化">
      <option name="closed" value="true" />
      <created>1754642645649</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754642645649</updated>
    </task>
    <task id="LOCAL-00002" summary="feat: 引入模型并添加加载动画">
      <option name="closed" value="true" />
      <created>1754895710626</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754895710626</updated>
    </task>
    <task id="LOCAL-00003" summary="feat: 全屏时触发画布尺寸更新">
      <option name="closed" value="true" />
      <created>1754896011074</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754896011074</updated>
    </task>
    <task id="LOCAL-00004" summary="feat: 部分模型更换材质">
      <option name="closed" value="true" />
      <created>1754906158292</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754906158292</updated>
    </task>
    <task id="LOCAL-00005" summary="feat: 性能优化">
      <option name="closed" value="true" />
      <created>1754967581343</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754967581343</updated>
    </task>
    <task id="LOCAL-00006" summary="feat: 添加去首部、尾部快捷按钮">
      <option name="closed" value="true" />
      <created>1754969575996</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754969575996</updated>
    </task>
    <task id="LOCAL-00007" summary="feat: 添加更多管道中轴线数据">
      <option name="closed" value="true" />
      <created>1755160226401</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755160226401</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="feat: 项目初始化" />
    <MESSAGE value="feat: 引入模型并添加加载动画" />
    <MESSAGE value="feat: 全屏时触发画布尺寸更新" />
    <MESSAGE value="feat: 部分模型更换材质" />
    <MESSAGE value="feat: 性能优化" />
    <MESSAGE value="feat: 添加去首部、尾部快捷按钮" />
    <MESSAGE value="feat: 添加更多管道中轴线数据" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 添加更多管道中轴线数据" />
  </component>
</project>